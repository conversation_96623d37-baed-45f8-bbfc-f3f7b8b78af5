<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\HostingRequestController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/hosts', [HomeController::class, 'hosts'])->name('hosts.index');
Route::get('/hosts/{user}', [HomeController::class, 'showHost'])->name('hosts.show');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Authenticated user routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [HomeController::class, 'dashboard'])->name('dashboard');
    
    // Hosting requests
    Route::prefix('hosting-requests')->name('hosting-requests.')->group(function () {
        Route::get('/', [HostingRequestController::class, 'index'])->name('index');
        Route::get('/create/{host}', [HostingRequestController::class, 'create'])->name('create');
        Route::post('/', [HostingRequestController::class, 'store'])->name('store');
        Route::get('/{hostingRequest}', [HostingRequestController::class, 'show'])->name('show');
        Route::patch('/{hostingRequest}/respond', [HostingRequestController::class, 'respond'])->name('respond');
        Route::delete('/{hostingRequest}', [HostingRequestController::class, 'destroy'])->name('destroy');
    });
});

// Admin routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    
    // User management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [AdminController::class, 'users'])->name('index');
        Route::get('/{user}', [AdminController::class, 'showUser'])->name('show');
        Route::get('/{user}/edit', [AdminController::class, 'editUser'])->name('edit');
        Route::patch('/{user}', [AdminController::class, 'updateUser'])->name('update');
        Route::delete('/{user}', [AdminController::class, 'deleteUser'])->name('destroy');
    });
});
