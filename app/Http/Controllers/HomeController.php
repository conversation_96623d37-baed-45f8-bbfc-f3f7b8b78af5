<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\HostingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth')->except(['index']);
    }

    /**
     * Show the application homepage.
     */
    public function index()
    {
        $hosts = User::where('is_host', true)
            ->where('is_active', true)
            ->take(6)
            ->get();

        return view('welcome', compact('hosts'));
    }

    /**
     * Show the application dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Get user's hosting requests
        $hostingRequests = $user->hostingRequests()
            ->with('host')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get received hosting requests (if user is a host)
        $receivedRequests = collect();
        if ($user->is_host) {
            $receivedRequests = $user->receivedHostingRequests()
                ->with('guest')
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        }

        return view('dashboard', compact('user', 'hostingRequests', 'receivedRequests'));
    }

    /**
     * Show hosts listing.
     */
    public function hosts(Request $request)
    {
        $query = User::where('is_host', true)->where('is_active', true);

        // Filter by city
        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->city . '%');
        }

        // Filter by country
        if ($request->filled('country')) {
            $query->where('country', 'like', '%' . $request->country . '%');
        }

        $hosts = $query->paginate(12);

        return view('hosts.index', compact('hosts'));
    }

    /**
     * Show host profile.
     */
    public function showHost(User $user)
    {
        if (!$user->is_host || !$user->is_active) {
            abort(404);
        }

        return view('hosts.show', compact('user'));
    }
}
